const bcrypt = require('bcrypt');

// The hash from the database
const storedHash = '$2b$10$mLEI4smzk0YQPfNx7KA5UOlHu.77V0VG/W9RqQmPBJA0KQNu4W5Hy';

// Test different possible passwords
const testPasswords = ['admin123', 'admin', 'password', 'Admin123', 'ADMIN123'];

console.log('Testing password hashes...\n');

async function testPasswords() {
  for (const password of testPasswords) {
    try {
      const isValid = await bcrypt.compare(password, storedHash);
      console.log(`Password: "${password}" -> ${isValid ? 'VALID ✓' : 'INVALID ✗'}`);
    } catch (error) {
      console.log(`Password: "${password}" -> ERROR: ${error.message}`);
    }
  }

  // Also generate a new hash for 'admin123' to compare
  try {
    const newHash = await bcrypt.hash('admin123', 10);
    console.log('\nNew hash for "admin123":', newHash);

    // Test the new hash
    const result = await bcrypt.compare('admin123', newHash);
    console.log('New hash validation:', result ? 'VALID ✓' : 'INVALID ✗');
  } catch (error) {
    console.log('Error generating new hash:', error.message);
  }
}

testPasswords();
