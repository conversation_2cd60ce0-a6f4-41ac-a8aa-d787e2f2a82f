const mysql = require('mysql2/promise');

async function checkMySQL() {
  console.log('Checking MySQL connection...\n');

  try {
    // Try to connect to MySQL
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: ''
    });

    console.log('✓ MySQL connection successful');
    
    // Check MySQL version
    const [rows] = await connection.query('SELECT VERSION() as version');
    console.log(`✓ MySQL version: ${rows[0].version}`);

    // Check if database exists
    const [databases] = await connection.query("SHOW DATABASES LIKE 'employee_management'");
    if (databases.length > 0) {
      console.log('✓ employee_management database exists');
    } else {
      console.log('✗ employee_management database does not exist');
      console.log('Creating database...');
      await connection.query('CREATE DATABASE employee_management');
      console.log('✓ Database created');
    }

    await connection.end();
    console.log('✓ MySQL check completed');

  } catch (error) {
    console.error('✗ MySQL connection failed:', error.message);
    console.log('\nPossible issues:');
    console.log('1. MySQL is not installed');
    console.log('2. MySQL service is not running');
    console.log('3. Wrong connection credentials');
    console.log('4. MySQL is running on a different port');
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n→ MySQL server is not running. Please start MySQL service.');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n→ Access denied. Check MySQL username/password.');
    }
  }
}

checkMySQL();
