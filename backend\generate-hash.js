const bcrypt = require('bcrypt');

async function generateHash() {
  try {
    const password = '123456';
    const hash = await bcrypt.hash(password, 10);
    console.log(`Password: ${password}`);
    console.log(`Hash: ${hash}`);
    
    // Verify the hash works
    const isValid = await bcrypt.compare(password, hash);
    console.log(`Verification: ${isValid ? 'SUCCESS' : 'FAILED'}`);
  } catch (error) {
    console.error('Error:', error.message);
  }
}

generateHash();
