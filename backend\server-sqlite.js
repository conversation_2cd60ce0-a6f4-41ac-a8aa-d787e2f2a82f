const express = require('express');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const session = require('express-session');
const bcrypt = require('bcrypt');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: 'http://localhost:5173', // Vite default port
  credentials: true
}));
app.use(express.json());
app.use(session({
  secret: 'employee-management-secret',
  resave: false,
  saveUninitialized: false,
  cookie: {
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Database setup
const dbPath = path.join(__dirname, 'employee_management.db');
const db = new sqlite3.Database(dbPath);

// Initialize database
const initializeDatabase = () => {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Create users table
      db.run(`
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username TEXT NOT NULL UNIQUE,
          password TEXT NOT NULL,
          first_name TEXT NOT NULL,
          last_name TEXT NOT NULL,
          email TEXT NOT NULL UNIQUE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Create departments table
      db.run(`
        CREATE TABLE IF NOT EXISTS departments (
          department_code TEXT PRIMARY KEY,
          department_name TEXT NOT NULL,
          gross_salary REAL NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Create employees table
      db.run(`
        CREATE TABLE IF NOT EXISTS employees (
          employee_number TEXT PRIMARY KEY,
          first_name TEXT NOT NULL,
          last_name TEXT NOT NULL,
          position TEXT NOT NULL,
          address TEXT,
          telephone TEXT,
          gender TEXT,
          hired_date DATE NOT NULL,
          department_code TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (department_code) REFERENCES departments(department_code)
        )
      `);

      // Create salaries table
      db.run(`
        CREATE TABLE IF NOT EXISTS salaries (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          employee_number TEXT NOT NULL,
          gross_salary REAL NOT NULL,
          total_deduction REAL NOT NULL,
          net_salary REAL NOT NULL,
          month DATE NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (employee_number) REFERENCES employees(employee_number) ON DELETE CASCADE
        )
      `);

      // Check if we need to insert sample data
      db.get('SELECT COUNT(*) as count FROM users', async (err, row) => {
        if (err) {
          reject(err);
          return;
        }

        if (row.count === 0) {
          try {
            // Insert default admin user
            const hashedPassword = await bcrypt.hash('admin123', 10);
            db.run(`
              INSERT INTO users (username, password, first_name, last_name, email)
              VALUES (?, ?, ?, ?, ?)
            `, ['admin', hashedPassword, 'Admin', 'User', '<EMAIL>']);

            // Insert sample departments
            const departments = [
              ['ENG', 'Engineering', 75000.00],
              ['HR', 'Human Resources', 65000.00],
              ['FIN', 'Finance', 70000.00],
              ['MKT', 'Marketing', 60000.00]
            ];

            departments.forEach(dept => {
              db.run(`
                INSERT INTO departments (department_code, department_name, gross_salary)
                VALUES (?, ?, ?)
              `, dept);
            });

            // Insert sample employees
            const employees = [
              ['EMP001', 'John', 'Doe', 'Software Engineer', '123 Main St, Anytown, USA', '************', 'Male', '2022-01-15', 'ENG'],
              ['EMP002', 'Jane', 'Smith', 'HR Manager', '456 Oak Ave, Somewhere, USA', '************', 'Female', '2021-05-20', 'HR'],
              ['EMP003', 'Michael', 'Johnson', 'Accountant', '789 Pine Rd, Nowhere, USA', '************', 'Male', '2022-03-10', 'FIN']
            ];

            employees.forEach(emp => {
              db.run(`
                INSERT INTO employees (employee_number, first_name, last_name, position, address, telephone, gender, hired_date, department_code)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
              `, emp);
            });

            console.log('✓ Sample data inserted');
          } catch (error) {
            console.error('Error inserting sample data:', error);
          }
        }

        console.log('✓ Database initialized successfully');
        resolve();
      });
    });
  });
};

// Middleware to check if user is authenticated
const isAuthenticated = (req, res, next) => {
  if (req.session.userId) {
    next();
  } else {
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Login route
app.post('/api/login', (req, res) => {
  const { username, password } = req.body;

  if (!username || !password) {
    return res.status(400).json({ message: 'Username and password are required' });
  }

  db.get('SELECT * FROM users WHERE username = ?', [username], async (err, user) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ message: 'Server error' });
    }

    if (!user) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    try {
      const isPasswordValid = await bcrypt.compare(password, user.password);

      if (!isPasswordValid) {
        return res.status(401).json({ message: 'Invalid credentials' });
      }

      // Set session
      req.session.userId = user.id;
      req.session.username = user.username;

      res.json({
        message: 'Login successful',
        user: {
          id: user.id,
          username: user.username,
          firstName: user.first_name,
          lastName: user.last_name,
          email: user.email
        }
      });
    } catch (error) {
      console.error('Password comparison error:', error);
      res.status(500).json({ message: 'Server error' });
    }
  });
});

// Get current user
app.get('/api/user', isAuthenticated, (req, res) => {
  db.get('SELECT id, username, first_name, last_name, email FROM users WHERE id = ?', [req.session.userId], (err, user) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ message: 'Server error' });
    }

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({
      id: user.id,
      username: user.username,
      firstName: user.first_name,
      lastName: user.last_name,
      email: user.email
    });
  });
});

// Logout
app.post('/api/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      return res.status(500).json({ message: 'Logout failed' });
    }
    res.json({ message: 'Logout successful' });
  });
});

// Initialize database and start server
initializeDatabase()
  .then(() => {
    app.listen(PORT, () => {
      console.log(`✓ Server running on port ${PORT}`);
      console.log(`✓ Database: ${dbPath}`);
      console.log('✓ Login credentials: admin / admin123');
    });
  })
  .catch((error) => {
    console.error('Failed to initialize database:', error);
    process.exit(1);
  });
