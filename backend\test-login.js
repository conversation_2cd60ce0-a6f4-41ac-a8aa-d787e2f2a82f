const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

async function testLogin() {
  try {
    console.log('Testing database connection and login...\n');

    // Create database connection
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'employee_management'
    });

    console.log('✓ Database connection successful');

    // Check if users table exists
    const [tables] = await connection.query("SHOW TABLES LIKE 'users'");
    if (tables.length === 0) {
      console.log('✗ Users table does not exist');
      return;
    }
    console.log('✓ Users table exists');

    // Get all users
    const [users] = await connection.query('SELECT * FROM users');
    console.log(`✓ Found ${users.length} users in database`);

    if (users.length === 0) {
      console.log('✗ No users found in database');
      
      // Create the admin user
      console.log('Creating admin user...');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      await connection.query(`
        INSERT INTO users (username, password, first_name, last_name, email)
        VALUES ('admin', ?, 'Admin', 'User', '<EMAIL>')
      `, [hashedPassword]);
      console.log('✓ Admin user created');
    } else {
      // Display users
      console.log('\nUsers in database:');
      users.forEach(user => {
        console.log(`- ID: ${user.id}, Username: ${user.username}, Name: ${user.first_name} ${user.last_name}`);
      });
    }

    // Test login with admin/admin123
    console.log('\nTesting login with admin/admin123...');
    const [adminUsers] = await connection.query('SELECT * FROM users WHERE username = ?', ['admin']);
    
    if (adminUsers.length === 0) {
      console.log('✗ Admin user not found');
    } else {
      const adminUser = adminUsers[0];
      console.log(`✓ Admin user found: ${adminUser.username}`);
      
      // Test password
      const isPasswordValid = await bcrypt.compare('admin123', adminUser.password);
      console.log(`Password test: ${isPasswordValid ? '✓ VALID' : '✗ INVALID'}`);
      
      if (!isPasswordValid) {
        console.log('Password hash in database:', adminUser.password);
        
        // Generate new hash and update
        console.log('Updating password hash...');
        const newHash = await bcrypt.hash('admin123', 10);
        await connection.query('UPDATE users SET password = ? WHERE username = ?', [newHash, 'admin']);
        console.log('✓ Password updated');
      }
    }

    await connection.end();
    console.log('\n✓ Test completed');

  } catch (error) {
    console.error('✗ Error:', error.message);
    
    if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\nDatabase does not exist. Creating it...');
      try {
        const connection = await mysql.createConnection({
          host: 'localhost',
          user: 'root',
          password: ''
        });
        
        await connection.query('CREATE DATABASE IF NOT EXISTS employee_management');
        console.log('✓ Database created');
        await connection.end();
        
        console.log('Please restart the backend server to initialize tables.');
      } catch (createError) {
        console.error('✗ Failed to create database:', createError.message);
      }
    }
  }
}

testLogin();
